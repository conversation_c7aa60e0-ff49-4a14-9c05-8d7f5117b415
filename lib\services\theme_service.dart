import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../theme/app_theme.dart';

class ThemeService extends ChangeNotifier {
  static const String _themeKey = 'isDarkMode';
  bool _isDarkMode = false;
  
  bool get isDarkMode => _isDarkMode;
  
  ThemeData get currentTheme => _isDarkMode ? AppTheme.darkTheme : AppTheme.lightTheme;
  
  // تهيئة الخدمة وتحميل الإعدادات المحفوظة
  Future<void> init() async {
    final prefs = await SharedPreferences.getInstance();
    _isDarkMode = prefs.getBool(_themeKey) ?? false;
    AppTheme.updateTheme(_isDarkMode);
    notifyListeners();
  }
  
  // تغيير الوضع المظلم
  Future<void> toggleTheme() async {
    _isDarkMode = !_isDarkMode;
    AppTheme.updateTheme(_isDarkMode);
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_themeKey, _isDarkMode);
    
    notifyListeners();
  }
  
  // تعيين الوضع المظلم مباشرة
  Future<void> setDarkMode(bool isDark) async {
    if (_isDarkMode == isDark) return;
    
    _isDarkMode = isDark;
    AppTheme.updateTheme(_isDarkMode);
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_themeKey, _isDarkMode);
    
    notifyListeners();
  }
  
  // الحصول على لون الخلفية الحالي
  Color get backgroundColor => _isDarkMode 
    ? AppTheme.darkBackgroundColor 
    : AppTheme.lightBackgroundColor;
    
  // الحصول على لون السطح الحالي
  Color get surfaceColor => _isDarkMode 
    ? AppTheme.darkSurfaceColor 
    : AppTheme.lightSurfaceColor;
    
  // الحصول على لون النص الأساسي الحالي
  Color get textPrimaryColor => _isDarkMode 
    ? AppTheme.darkTextPrimaryColor 
    : AppTheme.lightTextPrimaryColor;
    
  // الحصول على لون النص الثانوي الحالي
  Color get textSecondaryColor => _isDarkMode 
    ? AppTheme.darkTextSecondaryColor 
    : AppTheme.lightTextSecondaryColor;
    
  // الحصول على لون الحدود الحالي
  Color get borderColor => _isDarkMode 
    ? AppTheme.darkBorderColor 
    : AppTheme.lightBorderColor;
    
  // الحصول على ظلال الكروت المناسبة للوضع الحالي
  List<BoxShadow> get cardShadow => [
    BoxShadow(
      color: _isDarkMode 
        ? Colors.black.withValues(alpha: 0.3)
        : Colors.black.withValues(alpha: 0.05),
      blurRadius: 10,
      offset: const Offset(0, 4),
    ),
  ];
  
  // الحصول على ظلال مرفوعة مناسبة للوضع الحالي
  List<BoxShadow> get elevatedShadow => [
    BoxShadow(
      color: _isDarkMode 
        ? Colors.black.withValues(alpha: 0.4)
        : Colors.black.withValues(alpha: 0.1),
      blurRadius: 20,
      offset: const Offset(0, 8),
    ),
  ];
}
